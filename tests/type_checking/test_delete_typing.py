"""
Type checking tests for delete methods to verify proper typing support.

This file contains type annotations that should be properly recognized by my<PERSON>
and other type checkers after the typing improvements for delete methods.
"""

from typing import Optional
from pydantic import BaseModel
from sqlalchemy import Column, Integer, String, Boolean, DateTime
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy.ext.asyncio import AsyncSession
from fastcrud import FastCRUD


# Test models and schemas
class Base(DeclarativeBase):
    pass


class TestModel(Base):
    __tablename__ = "test_model"
    id = Column(Integer, primary_key=True)
    name = Column(String)
    is_deleted = Column(Boolean, default=False)
    deleted_at = Column(DateTime, nullable=True)


class CreateTestSchema(BaseModel):
    name: str


class UpdateTestSchema(BaseModel):
    name: Optional[str] = None


class UpdateTestInternalSchema(BaseModel):
    name: Optional[str] = None


class DeleteTestSchema(BaseModel):
    id: Optional[int] = None
    name: Optional[str] = None


class SelectTestSchema(BaseModel):
    id: int
    name: str


# Type-safe FastCRUD instance
TestCRUD = FastCRUD[
    TestModel,
    CreateTestSchema,
    UpdateTestSchema,
    UpdateTestInternalSchema,
    DeleteTestSchema,
    SelectTestSchema,
]


async def test_delete_method_typing(db: AsyncSession) -> None:
    """Test that delete method has proper typing support."""
    crud = TestCRUD(TestModel)
    
    # Test with filters parameter (should be properly typed)
    delete_filters = DeleteTestSchema(id=1)
    await crud.delete(db, filters=delete_filters)
    
    # Test with extra_filters (should be properly typed)
    await crud.delete(db, id=1)
    
    # Test with both filters and extra_filters (should be properly typed)
    await crud.delete(db, filters=delete_filters, name="test")
    
    # Test with allow_multiple parameter (should be properly typed)
    await crud.delete(db, filters=delete_filters, allow_multiple=True)
    
    # Test with commit parameter (should be properly typed)
    await crud.delete(db, filters=delete_filters, commit=False)


async def test_db_delete_method_typing(db: AsyncSession) -> None:
    """Test that db_delete method has proper typing support."""
    crud = TestCRUD(TestModel)
    
    # Test with filters parameter (should be properly typed)
    delete_filters = DeleteTestSchema(id=1)
    await crud.db_delete(db, filters=delete_filters)
    
    # Test with extra_filters (should be properly typed)
    await crud.db_delete(db, id=1)
    
    # Test with both filters and extra_filters (should be properly typed)
    await crud.db_delete(db, filters=delete_filters, name="test")
    
    # Test with allow_multiple parameter (should be properly typed)
    await crud.db_delete(db, filters=delete_filters, allow_multiple=True)
    
    # Test with commit parameter (should be properly typed)
    await crud.db_delete(db, filters=delete_filters, commit=False)


async def test_delete_method_return_type(db: AsyncSession) -> None:
    """Test that delete method return type is properly inferred."""
    crud = TestCRUD(TestModel)
    
    # The return type should be None
    result = await crud.delete(db, id=1)
    assert result is None


async def test_db_delete_method_return_type(db: AsyncSession) -> None:
    """Test that db_delete method return type is properly inferred."""
    crud = TestCRUD(TestModel)
    
    # The return type should be None
    result = await crud.db_delete(db, id=1)
    assert result is None


def test_fastcrud_generic_typing() -> None:
    """Test that FastCRUD generic typing works correctly."""
    # This should be properly typed with all generic parameters
    crud: FastCRUD[
        TestModel,
        CreateTestSchema,
        UpdateTestSchema,
        UpdateTestInternalSchema,
        DeleteTestSchema,
        SelectTestSchema,
    ] = FastCRUD(TestModel)
    
    # The model should be properly typed
    assert crud.model == TestModel


def test_delete_schema_typing() -> None:
    """Test that DeleteTestSchema is properly typed."""
    # This should be properly typed
    delete_schema = DeleteTestSchema(id=1, name="test")
    
    # Fields should be properly typed
    assert isinstance(delete_schema.id, int)
    assert isinstance(delete_schema.name, str)
    
    # Optional fields should work
    delete_schema_partial = DeleteTestSchema(id=1)
    assert delete_schema_partial.name is None


# Example usage that should work with proper typing
async def example_usage(db: AsyncSession) -> None:
    """Example usage that demonstrates proper typing."""
    # Create a properly typed FastCRUD instance
    chat_crud = FastCRUD[
        TestModel,
        CreateTestSchema,
        UpdateTestSchema,
        UpdateTestInternalSchema,
        DeleteTestSchema,
        SelectTestSchema,
    ](TestModel)
    
    # This should now have proper typing support (no "partially unknown" warnings)
    delete_filters = DeleteTestSchema(id=1)
    await chat_crud.delete(db, filters=delete_filters)
    
    # This should also work with proper typing
    await chat_crud.delete(db, id=1)
    
    # Combined usage should be properly typed
    await chat_crud.delete(db, filters=delete_filters, name="test", allow_multiple=True)
